import { ErrorCode } from "./ErrorCode";

// 错误信息映射
export const ErrorInfo: {
  [key in ErrorCode]: string;
} = {
  [ErrorCode.SUCCESS]: "授权验证通过，欢迎使用。",
  [ErrorCode.UNAUTHORIZED]: "[错误码 10001] 软件未授权，请使用授权码激活后使用。",
  [ErrorCode.AUTH_FILE_DAMAGED]: "[错误码 10002] 授权文件损坏，请使用授权码重新激活。",
  [ErrorCode.WRONG_MACHINECODE]: "[错误码 10003] 授权码与本机的机器码不匹配，请联系管理员获取正确的授权码。",
  [ErrorCode.AUTH_EXPIRE]: "[错误码 10004] 授权码已过期，请联系管理员获取新的授权码。",
  [ErrorCode.TIME_ANOMALY]: "[错误码 10005] 本地时间异常，授权校验失败。",
  [ErrorCode.NETWORK_ERROR]: "[错误码 10006] 网络请求超时，请稍后再试。",
  [ErrorCode.WRONG_ACTIVECODE]: "[错误码 10007] 授权码解析失败，请联系管理员获取正确的授权码。",
  [ErrorCode.ACTIVECODE_EXPIRE]: "[错误码 10008] 此授权码已失效，请联系管理员重新获取授权码。",
  [ErrorCode.WRONG_SOFTWARE]: "[错误码 10009] 此激活码不适用于当前软件，请确认软件名称是否正确。",
};
