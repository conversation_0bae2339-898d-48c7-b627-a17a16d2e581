# License Management Library

一个用于软件授权管理的TypeScript库，提供机器码生成、授权验证和激活码管理功能。

## 功能特性

- 机器码生成：基于硬件信息生成唯一标识
- AES-CBC加密：安全的数据加密和解密
- 授权管理：验证和管理软件授权状态
- 激活码生成：支持永久、限时和带有效期的激活码

## 安装

```bash
npm install license
```

## 快速开始

### 客户端使用示例

```typescript
import { myAuthMgr, ErrorCode } from 'license';

async function main() {
  const SOFTWARE_NAME = "VisualDebug"; // 软件名称
  
  // 检查授权状态
  const checkResult = await myAuthMgr.checkAuth(SOFTWARE_NAME);
  console.log(checkResult.errInfo);

  if (checkResult.errCode !== ErrorCode.SUCCESS) {
    // 获取机器码
    console.log("机器码:", myAuthMgr.getMachineCode());
    
    // 激活软件
    const keyCode = "YOUR_ACTIVATION_CODE";
    const activateResult = await myAuthMgr.activate(SOFTWARE_NAME, keyCode);

    if (activateResult.errCode !== ErrorCode.SUCCESS) {
      console.log(activateResult.errInfo);
      return;
    }
  }

  console.log("软件已授权");
}
```

### 激活码生成示例

```typescript
import { MyPojieManager } from 'license';

async function main() {
  const pojie = new MyPojieManager();
  const SOFTWARE_NAME = "VisualDebug"; // 软件名称
  const machineCode = "USER_MACHINE_CODE";
  const permissionId = "default"; // 权限ID

  // 生成永久激活码
  const permanentCode = await pojie.getActiveCode(SOFTWARE_NAME, machineCode, permissionId);
  
  // 生成3天激活码
  const currentTime = Math.floor(Date.now() / 1000);
  const threeDaysCode = await pojie.getActiveCode(
    SOFTWARE_NAME,
    machineCode,
    permissionId,
    currentTime,
    currentTime + 3 * 24 * 60 * 60
  );
  
  // 生成30天激活码（10分钟内有效）
  const thirtyDaysCode = await pojie.getActiveCode(
    SOFTWARE_NAME,
    machineCode,
    permissionId,
    currentTime,
    currentTime + 30 * 24 * 60 * 60,
    currentTime + 600
  );
}
```

## API文档

### MachineCodeGenerator

机器码生成器，用于生成基于硬件信息的唯一标识。

```typescript
const mcg = new MachineCodeGenerator(preStr: string, sufStr: string);
const machineCode = await mcg.getMachineCode();
```

### MyAESCBC

AES-CBC模式的加密解密工具。

```typescript
const aes = new MyAESCBC(key: string, iv: string);
const encrypted = aes.encrypt(plainText: string);
const decrypted = aes.decrypt(encryptedBuffer: Buffer);
```

### MyAuthManager

授权管理器，处理软件的授权验证和激活。

```typescript
// 获取机器码
const machineCode = myAuthMgr.getMachineCode();

// 检查授权状态
const checkResult = await myAuthMgr.checkAuth(softwareName: string);

// 激活软件
const activateResult = await myAuthMgr.activate(softwareName: string, activeCode: string);
```

### MyPojieManager

激活码生成工具，支持多种类型的激活码生成。

```typescript
const pojie = new MyPojieManager();

// 生成永久激活码
const code1 = await pojie.getActiveCode(softwareName, machineCode, permissionId);

// 生成限时激活码
const code2 = await pojie.getActiveCode(
  softwareName,
  machineCode,
  permissionId,
  startTime,
  endTime,
  activeEndTime
);
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 10000 | 成功 |
| 10001 | 未授权 |
| 10002 | 授权文件损坏 |
| 10003 | 机器码不匹配 |
| 10004 | 授权已过期 |
| 10005 | 时间异常 |
| 10006 | 网络错误 |
| 10007 | 激活码无效 |
| 10008 | 激活码已过期 |
| 10009 | 软件名称不匹配 |

## 开发

```bash
# 安装依赖
npm install

# 运行测试
npm test

# 构建
npm run build

# 运行示例
npm run example:client
npm run example:pojie
```

## License

ISC
