{"compilerOptions": {"target": "es2018", "module": "commonjs", "moduleResolution": "node", "declaration": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "rootDir": "./src", "types": ["mocha", "node"], "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "src/types/**/*"], "exclude": ["node_modules", "test", "dist"]}