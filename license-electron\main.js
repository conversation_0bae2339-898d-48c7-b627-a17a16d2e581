const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    autoHideMenuBar: true,
    frame: true
  });

  mainWindow.setMenuBarVisibility(false);
  mainWindow.loadFile('index.html');
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC 通信处理
ipcMain.handle('get-machine-code', async () => {
  const { myAuthMgr } = require('license');
  try {
    await myAuthMgr.init();
    const code = await myAuthMgr.getMachineCode();
    return code;
  } catch (err) {
    console.error('Error getting machine code:', err);
    dialog.showErrorBox('错误', err.message);
    return `Error: ${err.message}`;
  }
});

ipcMain.handle('generate-activation-code', async (event, { softwareName, machineCode, codeType, days, permissionId }) => {
  const { MyPojieManager } = require('license');
  try {
    const pojie = new MyPojieManager();
    const curTime = Math.floor(Date.now() / 1000);

    let code;
    if (codeType === '永久激活码') {
      code = await pojie.getActiveCode(softwareName, machineCode, permissionId);
    } else if (codeType === '临时激活码') {
      code = await pojie.getActiveCode(
        softwareName,
        machineCode,
        permissionId,
        curTime,
        curTime + days * 24 * 60 * 60
      );
    } else { // 限时激活的临时激活码
      code = await pojie.getActiveCode(
        softwareName,
        machineCode,
        permissionId,
        curTime,
        curTime + days * 24 * 60 * 60,
        curTime + 600 // 10分钟内有效
      );
    }
    return code;
  } catch (err) {
    console.error('Error generating activation code:', err);
    dialog.showErrorBox('错误', err.message);
    return `Error: ${err.message}`;
  }
});
