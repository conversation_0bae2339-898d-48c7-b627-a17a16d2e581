const typescript = require('@rollup/plugin-typescript');
const commonjs = require('@rollup/plugin-commonjs');
const nodeResolve = require('@rollup/plugin-node-resolve');
const json = require('@rollup/plugin-json');

module.exports = {
  input: ['src/index.ts', 'src/managers/MyAuthManager.ts', 'src/managers/MyPojieManager.ts'],
  output: [
    {
      dir: 'dist',
      format: 'cjs',
      sourcemap: true,
      preserveModules: true,
      preserveModulesRoot: 'src'
    }
  ],
  external: ['date-fns', 'hi-base32', 'node-wmi', 'crypto', 'fs', 'https', 'uuid'],
  plugins: [
    typescript({
      tsconfig: './tsconfig.json'
    }),
    nodeResolve({
      preferBuiltins: true
    }),
    commonjs({
      ignoreDynamicRequires: true
    }),
    json()
  ]
};
