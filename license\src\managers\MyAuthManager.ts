import { createHash } from "crypto";
import { writeFileSync, readFileSync, existsSync } from "fs";
import { get } from "https";
import { format } from "date-fns";
import { ErrorCode } from "../errors/ErrorCode";
import { ErrorInfo } from "../errors/ErrorInfo";
import { MachineCodeGenerator } from "../core/MachineCodeGenerator";
import { BaseAuthManager } from "../core/BaseAuthManager";
import { MyAESCBC } from "../crypto/MyAESCBC";
import * as base32 from "hi-base32";

interface AuthData {
  software: string;
  code: string;
  startTs: number;
  endTs: number;
  active_endTs?: number;
  permissionId: string;
}

/**
 * 授权管理器实现类
 * 提供软件授权的具体实现，包括授权验证、激活等功能
 */
class MyAuthManager extends BaseAuthManager {
  private readonly aes: MyAESCBC;
  private readonly preStr = "HJDKAH";
  private readonly sufStr = "SDFDTY";
  protected readonly configPath = "./regisiter.bin";
  private mcg = new MachineCodeGenerator(this.preStr, this.sufStr);
  protected machineCode: string = "";
  protected initialized = false;
  public mEndTime = 0;
  protected permissionId: string = "";

  constructor() {
    super();
    this.aes = new MyAESCBC("9B8FD68A366F4D03", "305FB72D83134CA0");
  }

  /**
   * 初始化授权管理器
   * 获取机器码并完成初始化工作
   */
  public async init(): Promise<void> {
    if (this.initialized) return;
    await this.initializeMachineCode();
    this.initialized = true;
  }

  /**
   * 初始化机器码
   * 使用MachineCodeGenerator生成并保存机器码
   */
  private async initializeMachineCode(): Promise<void> {
    this.machineCode = (await this.mcg.getMachineCode()).toUpperCase();
  }

  /**
   * 获取可靠的时间戳
   * 1. 尝试从多个时间源获取时间
   * 2. 如果网络时间获取失败，使用本地时间
   * 3. 对比网络时间和本地时间，检测时间异常
   */
  /**
   * 获取可靠的时间戳
   * 优先使用网络时间，如果网络不可用则降级使用本地时间
   * @returns 时间戳（秒）
   */
  protected async getCurrentTimestamp(): Promise<number> {
    // 获取本地时间
    const localTime = Math.floor(Date.now() / 1000);

    // 尝试获取网络时间
    try {
      const networkTime = await new Promise<number>((resolve, reject) => {
        const timeoutId = setTimeout(
          () => reject(new Error("Network timeout")),
          3000
        );

        get("https://b2m.sieyuan.com.cn/", (res) => {
          clearTimeout(timeoutId);
          const date = res.headers.date;
          if (date) {
            const serverTime = new Date(date).getTime() / 1000 + 8 * 3600; // 转换为东八区
            resolve(Math.floor(serverTime));
          } else {
            reject(new Error("No date header"));
          }
        }).on("error", reject);
      });

      // 检查本地时间是否异常（与网络时间相差超过24小时）
      const timeDiff = Math.abs(networkTime - localTime);
      if (timeDiff > 24 * 3600) {
        console.warn(
          "Local time differs from network time by more than 24 hours"
        );
      }
      console.log("网络时间", networkTime);
      // 总是返回网络时间
      return networkTime;
    } catch (error) {
      // 如果获取网络时间失败，降级使用本地时间
      console.warn("Failed to get network time, using local time:", error);
      return localTime;
    }
  }

  /**
   * 读取授权文件
   * 从磁盘读取并解密授权信息
   * @returns 授权文件内容和错误码
   */
  private readAuthFile(): { errCode: ErrorCode; data?: AuthData } {
    if (!existsSync(this.configPath)) {
      return { errCode: ErrorCode.UNAUTHORIZED };
    }

    try {
      const encrypted = readFileSync(this.configPath);
      const decryptedText = this.aes.decrypt(encrypted);
      const decrypted = JSON.parse(decryptedText);
      return this.parseAuthInfo(decrypted);
    } catch (error) {
      const cryptoError = error as { code?: string };
      if (
        error instanceof Error &&
        "code" in error &&
        ["ERR_OSSL_WRONG_FINAL_BLOCK_LENGTH", "ERR_OSSL_BAD_DECRYPT"].includes(
          cryptoError.code || ""
        )
      ) {
        return { errCode: ErrorCode.AUTH_FILE_DAMAGED };
      }
      console.error("Read auth file error:", error);
      return { errCode: ErrorCode.AUTH_FILE_DAMAGED };
    }
  }

  /**
   * 解析授权信息
   * 验证授权数据的完整性和有效性
   * @param data 需要解析的授权数据
   * @returns 解析后的授权信息和错误码
   */
  private parseAuthInfo(data: unknown): {
    errCode: ErrorCode;
    data?: AuthData;
  } {
    // 类型检查
    if (!data || typeof data !== "object") {
      return { errCode: ErrorCode.WRONG_ACTIVECODE };
    }

    const authObj = data as Record<string, unknown>;

    // 属性存在性检查
    if (
      !("software" in authObj) ||
      !("code" in authObj) ||
      !("startTs" in authObj) ||
      !("endTs" in authObj)
    ) {
      return { errCode: ErrorCode.WRONG_ACTIVECODE };
    }

    // 类型转换和验证
    const code = String(authObj.code).toUpperCase();
    const startTs = Number(authObj.startTs);
    const endTs = Number(authObj.endTs);
    const software = String(authObj.software);
    const active_endTs = authObj.active_endTs
      ? Number(authObj.active_endTs)
      : 0;

    // 验证数据有效性
    if (
      !code ||
      isNaN(startTs) ||
      isNaN(endTs) ||
      (authObj.active_endTs !== undefined && isNaN(active_endTs))
    ) {
      return { errCode: ErrorCode.WRONG_ACTIVECODE };
    }

    return {
      errCode: ErrorCode.SUCCESS,
      data: {
        code,
        startTs,
        endTs,
        active_endTs,
        software: software,
        permissionId: String(authObj.permissionId),
      },
    };
  }

  /**
   * 更新授权文件
   * 将新的授权信息加密并写入文件
   * @param curTs 当前时间戳
   * @param startTs 授权开始时间
   * @param endTs 授权结束时间
   */
  private updateAuthFile(
    softwareName: string,
    curTs: number,
    startTs: number,
    endTs: number,
    permissionId: string
  ): void {
    const newStartTs =
      curTs >= endTs ? endTs : curTs <= startTs ? startTs : curTs;
    const saveInfo = {
      software: softwareName,
      code: this.machineCode,
      startTs: newStartTs,
      endTs: endTs,
      permissionId: permissionId,
    };
    console.log(this.configPath);
    writeFileSync(this.configPath, this.aes.encrypt(JSON.stringify(saveInfo)));
  }

  /**
   * 生成错误信息
   * 根据错误码生成对应的错误信息
   * @param errCode 错误码
   * @returns 错误信息对象
   */
  private errorInfo(errCode: ErrorCode): {
    errCode: ErrorCode;
    errInfo: string;
    permissionId?: string;
  } {
    let info = ErrorInfo[errCode];
    if (errCode === ErrorCode.SUCCESS && this.mEndTime > 0) {
      info += `授权有效期截至：${format(
        this.mEndTime * 1000,
        "yyyy-MM-dd HH:mm:ss"
      )}`;
    }
    return {
      errCode,
      errInfo: info,
      permissionId: this.permissionId,
    };
  }

  /**
   * 检查授权状态
   * 验证当前软件的授权是否有效
   * @returns 授权检查结果
   */
  public async checkAuth(
    softwareName: string
  ): Promise<ReturnType<MyAuthManager["errorInfo"]>> {
    if (!this.initialized) await this.init();

    const config = this.readAuthFile();
    if (config.errCode !== ErrorCode.SUCCESS) {
      return this.errorInfo(config.errCode);
    }

    const data = config.data!;
    const curTs = await this.getCurrentTimestamp();

    if (curTs >= data.endTs) {
      return this.errorInfo(ErrorCode.AUTH_EXPIRE);
    }

    if (curTs <= data.startTs) {
      return this.errorInfo(ErrorCode.TIME_ANOMALY);
    }

    this.permissionId = data.permissionId; // 保存权限ID
    this.updateAuthFile(
      softwareName,
      curTs,
      data.startTs,
      data.endTs,
      data.permissionId
    );
    this.mEndTime = data.endTs;
    return this.errorInfo(ErrorCode.SUCCESS);
  }

  /**
   * 激活授权
   * 解析激活码并验证其有效性
   * @param activeCode 激活码
   * @returns 激活结果
   */
  public async activate(
    softwareName: string,
    activeCode: string
  ): Promise<ReturnType<MyAuthManager["errorInfo"]>> {
    if (!this.initialized) await this.init();

    try {
      // 记录一下关键信息
      console.debug("激活信息：", {
        machineCode: this.machineCode,
        activeCode,
      });

      // 解码和解密
      const decodedBytes = base32.decode.asBytes(activeCode.replace(/=+$/, ""));
      const decoded = Buffer.from(decodedBytes);
      const decrypted = this.aes.decrypt(decoded);

      // 打印解密结果
      console.debug("解密数据:", decrypted);

      let decryptedJson;
      try {
        decryptedJson = JSON.parse(decrypted);
        console.debug("解析数据:", decryptedJson);
      } catch (error) {
        console.error("JSON解析错误:", error);
        return this.errorInfo(ErrorCode.WRONG_ACTIVECODE);
      }

      const authInfo = this.parseAuthInfo(decryptedJson);
      if (authInfo.errCode !== ErrorCode.SUCCESS) {
        return this.errorInfo(authInfo.errCode);
      }

      const data = authInfo.data!;
      console.debug("对比机器码:", {
        stored: this.machineCode,
        received: data.code,
      });

      const curTs = await this.getCurrentTimestamp();

      // 检查软件名称和机器码是否匹配
      if (softwareName !== data.software) {
        return this.errorInfo(ErrorCode.WRONG_SOFTWARE);
      }

      if (this.machineCode !== data.code) {
        return this.errorInfo(ErrorCode.WRONG_MACHINECODE);
      }

      // 检查激活码本身是否过期
      if (data.active_endTs && curTs >= data.active_endTs) {
        return this.errorInfo(ErrorCode.ACTIVECODE_EXPIRE);
      }

      // 检查授权时间有效性
      if (curTs >= data.endTs) {
        this.mEndTime = data.endTs;
        return this.errorInfo(ErrorCode.AUTH_EXPIRE);
      }

      // 只有当所有验证都通过后才更新授权文件
      this.permissionId = data.permissionId; // 保存权限ID
      this.updateAuthFile(
        softwareName,
        curTs,
        data.startTs,
        data.endTs,
        data.permissionId
      );
      this.mEndTime = data.endTs;
      return this.errorInfo(ErrorCode.SUCCESS);
    } catch (error) {
      console.error("激活失败:", error);
      return this.errorInfo(ErrorCode.WRONG_ACTIVECODE);
    }
  }

  /**
   * 获取机器码
   * @returns 当前设备的机器码
   */
  public async getMachineCode(): Promise<string> {
    if (!this.initialized) await this.init();
    return this.machineCode;
  }
}

// 创建默认实例
const myAuthMgr = new MyAuthManager();
export { myAuthMgr };
export type { MyAuthManager };
