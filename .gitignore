# Node依赖
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# 构建输出
dist/
build/
out/
*.tsbuildinfo

# 环境变量和密钥
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.pem

# 日志
logs/
*.log

# 测试覆盖率
coverage/

# 编辑器/IDE配置
.idea/
.vscode/
.DS_Store
*.swp
*.swo
*~

# Electron打包
electron-builder.yml
electron-out/
release/

# 缓存
.cache/
.npm/
.eslintcache

# 其他
*.bak
*.tmp
.tmp/ 