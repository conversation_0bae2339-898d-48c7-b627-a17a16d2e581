{"name": "license", "version": "1.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "run-s clean build:*", "build:esbuild": "esbuild ./src/index.ts --bundle --platform=node --outfile=dist/index.js", "build:types": "tsc --emitDeclarationOnly --outDir dist", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "prepare": "npm run build", "example:client": "ts-node example/clientExample.ts", "example:pojie": "ts-node example/pojieExample.ts", "prepublishOnly": "npm run build"}, "keywords": ["license", "auth", "activation", "software-license"], "author": "<PERSON><PERSON>uan", "license": "ISC", "description": "Software license key generation and validation library", "dependencies": {"@types/jest": "^29.5.14", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "hi-base32": "^0.5.1", "node-wmi": "^0.0.5", "uuid": "^11.1.0"}, "devDependencies": {"@types/chai": "^5.2.2", "@types/mocha": "^10.0.10", "@types/node": "^22.15.18", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "chai": "^5.2.0", "cross-env": "^7.0.3", "esbuild": "^0.25.4", "mocha": "^11.2.2", "npm-run-all": "^4.1.5", "rimraf": "^5.0.10", "sinon": "^20.0.0", "typescript": "^5.8.3"}, "files": ["dist", "README.md", "LICENSE", "package.json"], "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public"}}