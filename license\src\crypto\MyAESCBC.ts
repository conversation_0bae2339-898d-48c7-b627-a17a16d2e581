import { createCipheriv, createDecipheriv } from 'crypto';

/**
 * AES CBC 加密实现
 * 提供加密和解密功能
 */
export class MyAESCBC {
  private key: string;
  private iv: string;

  constructor(key: string, iv: string) {
    this.key = key;
    this.iv = iv;
  }

  // 加密方法
  encrypt(plainText: string): Buffer {
    // 实现加密逻辑
    const cipher = createCipheriv('aes-128-cbc', Buffer.from(this.key), Buffer.from(this.iv));
    const encrypted = Buffer.concat([cipher.update(plainText), cipher.final()]);
    return encrypted;
  }

  // 解密方法
  decrypt(cipherText: Buffer): string {
    // 实现解密逻辑
    const decipher = createDecipheriv('aes-128-cbc', Buffer.from(this.key), Buffer.from(this.iv));
    const decrypted = Buffer.concat([decipher.update(cipherText), decipher.final()]);
    return decrypted.toString();
  }
}
