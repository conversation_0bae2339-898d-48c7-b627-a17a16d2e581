const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 获取DOM元素
const softwareNameInput = document.getElementById('software-name');
const permissionIdSelect = document.getElementById('permission-id');
const machineCodeTextarea = document.getElementById('machine-code');
const licenseTypeSelect = document.getElementById('license-type');
const validDaysSelect = document.getElementById('valid-days');
const generateBtn = document.getElementById('generate-btn');
const licenseDisplay = document.getElementById('license-display');
const copyBtn = document.getElementById('copy-btn');
const alertDiv = document.getElementById('alert');

// 显示提示信息
function showAlert(message, type = 'info', duration = 3000) {
  alertDiv.textContent = message;
  alertDiv.className = `alert alert-${type}`;
  alertDiv.style.display = 'block';
  alertDiv.style.animation = 'fadeIn 0.3s ease';

  // 自动消失
  if (duration > 0) {
    setTimeout(() => {
      alertDiv.style.animation = 'fadeOut 0.3s ease';
      setTimeout(() => {
        alertDiv.style.display = 'none';
      }, 300);
    }, duration);
  }
}

// 页面加载完成后自动获取机器码
document.addEventListener('DOMContentLoaded', async () => {
  try {
    const machineCode = await ipcRenderer.invoke('get-machine-code');
    if (machineCode.startsWith('Error:')) {
      showAlert(machineCode, 'error');
      return;
    }
    machineCodeTextarea.value = machineCode;
    showAlert('机器码获取成功', 'success');
  } catch (err) {
    showAlert(`获取机器码失败: ${err.message}`, 'error');
  }
});

// 监听激活码类型变化
licenseTypeSelect.addEventListener('change', () => {
  validDaysSelect.disabled = licenseTypeSelect.value === '永久激活码';
});

// 生成激活码
generateBtn.addEventListener('click', async () => {
  const softwareName = softwareNameInput.value.trim();
  const permissionId = permissionIdSelect.value;
  const machineCode = machineCodeTextarea.value.trim();
  
  if (!softwareName) {
    showAlert('请输入软件名称！', 'error');
    softwareNameInput.focus();
    return;
  }

  if (!machineCode) {
    showAlert('请等待机器码生成完成！', 'error');
    return;
  }

  try {
    const code = await ipcRenderer.invoke('generate-activation-code', {
      softwareName,
      permissionId,
      machineCode,
      codeType: licenseTypeSelect.value,
      days: parseInt(validDaysSelect.value)
    });

    if (code.startsWith('Error:')) {
      showAlert(code, 'error');
      return;
    }

    licenseDisplay.value = code;
    showAlert('激活码生成成功！', 'success');
  } catch (err) {
    showAlert(`生成激活码失败: ${err.message}`, 'error');
  }
});

// 复制激活码
copyBtn.addEventListener('click', () => {
  const licenseCode = licenseDisplay.value.trim();
  if (!licenseCode) {
    showAlert('请先生成激活码！', 'info');
    return;
  }

  navigator.clipboard.writeText(licenseCode)
    .then(() => {
      showAlert('激活码已复制到剪贴板', 'success');
    })
    .catch(err => {
      showAlert(`复制失败: ${err.message}`, 'error');
    });
});
