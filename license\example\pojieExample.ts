import { MyPojieManager } from "../src/managers/MyPojieManager";
import { myAuthMgr } from "../src/managers/MyAuthManager";

async function main() {
  await myAuthMgr.init();  // 确保初始化完成
  
  // 获取用户的机器码
  const machineCode = await myAuthMgr.getMachineCode();
  console.log("用户机器码:", machineCode);
  
  const pojie = new MyPojieManager();
  const curTime = Math.floor(Date.now() / 1000);
  const permissionId = "default"; // 默认权限ID

  const softwareNames = ["VisualDebug", "WaveAnalysis"];

  for (const softwareName of softwareNames) {
    console.log(`\n生成 ${softwareName} 的激活码：`);

    // 1. 生成永久激活码
    const permanentCode = await pojie.getActiveCode(
      softwareName,
      machineCode,
      permissionId
    );
    console.log("\n永久激活码:", permanentCode);

    // 2. 生成3天临时激活码
    const threeDaysCode = await pojie.getActiveCode(
      softwareName,
      machineCode,
      permissionId,
      curTime,
      curTime + 3 * 24 * 60 * 60
    );
    console.log("\n3天临时激活码:", threeDaysCode);

    // 3. 生成30天激活码（10分钟内有效）
    const thirtyDaysCode = await pojie.getActiveCode(
      softwareName,
      machineCode,
      permissionId,
      curTime,
      curTime + 30 * 24 * 60 * 60, // 30天使用期限
      curTime + 600 // 激活码10分钟内有效
    );
    console.log("\n30天激活码（10分钟内有效）:", thirtyDaysCode);
  }
}

// 使用方法说明
console.log("=== 激活码生成工具 ===");
console.log("本工具用于生成软件激活码，支持以下类型：");
console.log("1. 永久激活码");
console.log("2. 临时激活码（指定过期时间）");
console.log("3. 限时激活的临时激活码（激活码本身有效期+软件使用期限）");
console.log("==================");

main().catch(console.error);
