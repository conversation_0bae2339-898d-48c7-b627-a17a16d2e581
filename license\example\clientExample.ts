import { ErrorCode } from "../src/errors/ErrorCode";
import { myAuthMgr } from "../src/managers/MyAuthManager";

const SOFTWARE_NAME = "VisualDebug";

async function main() {
  // 检查授权状态
  const checkResult = await myAuthMgr.checkAuth(SOFTWARE_NAME);
  console.log(checkResult.errInfo);

  if (checkResult.errCode !== ErrorCode.SUCCESS) {
    // 获取机器码
    const machineCode = await myAuthMgr.getMachineCode();
    console.log("请联系管理员，机器码:", machineCode);
    
    // 在实际应用中，这里应该是用户输入激活码
    const keyCode = "YOUR_ACTIVATION_CODE"; 
    const activateResult = await myAuthMgr.activate(SOFTWARE_NAME, keyCode);

    if (activateResult.errCode !== ErrorCode.SUCCESS) {
      console.log(activateResult.errInfo);
      return;
    }
  }

  console.log("软件已授权，开始运行...");
}

main().catch(console.error);
