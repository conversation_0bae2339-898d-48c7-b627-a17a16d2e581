# 软件授权管理系统

一个完整的软件授权解决方案，包含授权管理库和授权码生成工具。

## 项目结构

此项目由两个主要部分组成：

1. **license** - 授权管理核心库
   - 提供机器码生成、授权验证和激活功能的TypeScript库
   - 可集成到任何Node.js应用程序中

2. **license-electron** - 授权码生成桌面应用
   - 基于Electron构建的授权码生成工具
   - 方便为用户生成不同类型的激活码

## 核心功能

- **机器码生成**：基于硬件信息生成唯一标识
- **加密保护**：使用AES-CBC加密保护授权数据
- **多种授权类型**：支持永久授权、临时授权和有效期授权
- **简单集成**：易于集成到各种软件中

## 快速开始

### 安装授权库

```bash
cd license
npm install
npm run build
```

### 运行授权码生成工具

```bash
cd license-electron
npm install
npm start
```

### 构建授权码生成工具

```bash
cd license-electron
npm run build
```

## 使用说明

### 在应用中集成授权库

1. 安装库：`npm install ../license`
2. 导入并初始化：

```javascript
const { myAuthMgr } = require('license');

// 检查授权状态
async function checkLicense(softwareName) {
  const result = await myAuthMgr.checkAuth(softwareName);
  return result;
}

// 激活软件
async function activateSoftware(softwareName, activationCode) {
  const result = await myAuthMgr.activate(softwareName, activationCode);
  return result;
}
```

### 使用授权码生成工具

1. 运行桌面应用
2. 输入软件名称和机器码
3. 选择激活码类型(永久/临时/限时)
4. 生成激活码并分享给用户

## 技术栈

- **授权库**：TypeScript, Node.js
- **桌面工具**：Electron, JavaScript

## 许可证

ISC 