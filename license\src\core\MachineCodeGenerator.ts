import * as crypto from "crypto";
import * as os from "os";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

type HardwareInfo = {
  macAddress: string;
  cpuSerial: string;
  diskSerial: string;
  boardSerial: string;
};

/**
 * 机器码生成器
 * 用于生成基于硬件信息的唯一机器码
 */
export class MachineCodeGenerator {
  /** 机器码前缀字符串 */
  private preStr: string;
  /** 机器码后缀字符串 */
  private sufStr: string;

  /**
   * 创建机器码生成器实例
   * @param preStr 机器码前缀字符串
   * @param sufStr 机器码后缀字符串
   */
  constructor(preStr: string, sufStr: string) {
    this.preStr = preStr;
    this.sufStr = sufStr;
  }

  /**
   * 获取MAC地址
   * @returns MAC地址字符串
   */
  private async getMacAddress(): Promise<string> {
    // try {
    //   const interfaces = os.networkInterfaces();
    //   for (const iface of Object.values(interfaces)) {
    //     if (!iface) continue;
    //     for (const info of iface) {
    //       if (!info.internal && info.mac !== '00:00:00:00:00:00') {
    //         return info.mac.replace(/:/g, '').toUpperCase();
    //       }
    //     }
    //   }
    //   throw new Error('No valid MAC address found');
    // } catch (error) {
    //   console.error('获取MAC地址失败:', error);
    //   return 'FFFFFFFFFFFF';
    // }
    return "FFFFFFFFFFFF";
  }

  /**
   * 执行WMIC命令并获取结果
   */
  private async executeWmicCommand(command: string): Promise<string> {
    try {
      const { stdout } = await execAsync(`wmic ${command}`);
      const lines = stdout.split('\n').map(line => line.trim()).filter(Boolean);
      // 通常第二行包含实际值（第一行是标题）
      return lines.length > 1 ? lines[1] : '';
    } catch (error) {
      console.error(`执行WMIC命令失败 (${command}):`, error);
      return '';
    }
  }

  /**
   * 获取硬件信息
   * 包括MAC地址、CPU序列号、硬盘序列号和主板序列号
   * @returns 硬件信息对象
   */
  private async getHardwareInfo(): Promise<HardwareInfo> {
    try {
      console.log("开始获取硬件信息...");
      
      // 获取MAC地址
      const macAddress = await this.getMacAddress();
      console.log("MAC地址:", macAddress);

      // 获取CPU信息
      const cpuSerial = await this.executeWmicCommand('cpu get processorid');
      console.log("CPU序列号:", cpuSerial || 'ABCDEFGHIJKLMNOP');

      // 获取硬盘信息
      const diskSerial = await this.executeWmicCommand('diskdrive get serialnumber');
      console.log("硬盘序列号:", diskSerial || 'WD-ABCDEFGHIJKL');

      // 获取主板信息
      const boardSerial = await this.executeWmicCommand('baseboard get serialnumber');
      console.log("主板序列号:", boardSerial || 'ABCDEFGHIJKLMN');

      return {
        macAddress: macAddress.substring(0, 12),
        cpuSerial: (cpuSerial || 'ABCDEFGHIJKLMNOP').substring(0, 16),
        diskSerial: (diskSerial || 'WD-ABCDEFGHIJKL').substring(0, 15),
        boardSerial: (boardSerial || 'ABCDEFGHIJKLMN').substring(0, 14)
      };
    } catch (error) {
      console.error("获取硬件信息失败:", error);
      // 如果获取硬件信息失败，使用默认值
      return {
        macAddress: "FFFFFFFFFFFF",
        cpuSerial: "ABCDEFGHIJKLMNOP",
        diskSerial: "WD-ABCDEFGHIJKL",
        boardSerial: "ABCDEFGHIJKLMN",
      };
    }
  }

  /**
   * 生成机器码
   * 基于硬件信息生成唯一的机器码
   * @returns 生成的机器码
   */
  async getMachineCode(): Promise<string> {
    const { macAddress, cpuSerial, diskSerial, boardSerial } =
      await this.getHardwareInfo();
    const combined = `${this.preStr}${macAddress}${cpuSerial}${diskSerial}${boardSerial}${this.sufStr}`;
    console.log("组合后的字符串:", combined);
    const code = crypto
      .createHash("md5")
      .update(combined, "utf8")
      .digest("hex")
      .toUpperCase();
    console.log("生成后的机器码========:", code);
    return code;
    
  }
}
